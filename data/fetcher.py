"""
数据获取模块
"""
import pandas as pd
import tushare as ts
import akshare as ak
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from utils.config import config
from utils.logger import get_logger
from utils.helpers import get_latest_trading_day, add_stock_suffix

logger = get_logger(__name__)


class DataFetcher:
    """数据获取器基类"""
    
    def __init__(self):
        self.data_config = config.get_data_source_config()
        self._init_data_sources()
    
    def _init_data_sources(self):
        """初始化数据源"""
        # 初始化Tushare
        if self.data_config.get('tushare', {}).get('enabled', False):
            token = self.data_config.get('tushare', {}).get('token')
            if token and token != 'your_tushare_token_here':
                ts.set_token(token)
                self.ts_pro = ts.pro_api()
                logger.info("Tushare数据源初始化成功")
            else:
                self.ts_pro = None
                logger.warning("Tushare token未配置，将使用免费接口")
        else:
            self.ts_pro = None
    
    def get_stock_list(self) -> pd.DataFrame:
        """
        获取股票列表
        
        Returns:
            pd.DataFrame: 股票列表数据
        """
        try:
            if self.ts_pro:
                # 使用Tushare Pro接口
                df = self.ts_pro.stock_basic(
                    exchange='',
                    list_status='L',
                    fields='ts_code,symbol,name,area,industry,market,list_date'
                )
                df['code'] = df['symbol']
                df['stock_name'] = df['name']
            else:
                # 使用AKShare免费接口
                df = ak.stock_info_a_code_name()
                df.columns = ['code', 'stock_name']
                df['ts_code'] = df['code'].apply(add_stock_suffix)
            
            logger.info(f"获取股票列表成功，共{len(df)}只股票")
            return df
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return pd.DataFrame()
    
    def get_daily_data(self, 
                      code: str, 
                      start_date: str = None, 
                      end_date: str = None) -> pd.DataFrame:
        """
        获取日线数据
        
        Args:
            code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            pd.DataFrame: 日线数据
        """
        try:
            if end_date is None:
                end_date = get_latest_trading_day()
            
            if start_date is None:
                start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=365)).strftime('%Y-%m-%d')
            
            if self.ts_pro:
                # 使用Tushare Pro接口
                ts_code = add_stock_suffix(code)
                df = self.ts_pro.daily(
                    ts_code=ts_code,
                    start_date=start_date.replace('-', ''),
                    end_date=end_date.replace('-', '')
                )
                if not df.empty:
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
                    df = df.sort_values('trade_date')
            else:
                # 使用AKShare免费接口
                df = ak.stock_zh_a_hist(
                    symbol=code,
                    period="daily",
                    start_date=start_date.replace('-', ''),
                    end_date=end_date.replace('-', ''),
                    adjust=""
                )
                if not df.empty:
                    df.columns = ['trade_date', 'open', 'close', 'high', 'low', 'vol', 'amount']
                    df['trade_date'] = pd.to_datetime(df['trade_date'])
            
            logger.debug(f"获取{code}日线数据成功，共{len(df)}条记录")
            return df
            
        except Exception as e:
            logger.error(f"获取{code}日线数据失败: {e}")
            return pd.DataFrame()
    
    def get_basic_info(self, code: str = None) -> pd.DataFrame:
        """
        获取股票基本信息
        
        Args:
            code: 股票代码，为None时获取所有股票
        
        Returns:
            pd.DataFrame: 基本信息数据
        """
        try:
            if self.ts_pro:
                # 使用Tushare Pro接口
                if code:
                    ts_code = add_stock_suffix(code)
                    df = self.ts_pro.daily_basic(ts_code=ts_code, trade_date=get_latest_trading_day().replace('-', ''))
                else:
                    df = self.ts_pro.daily_basic(trade_date=get_latest_trading_day().replace('-', ''))
            else:
                # 使用AKShare免费接口
                if code:
                    df = ak.stock_individual_info_em(symbol=code)
                else:
                    # AKShare没有批量获取基本信息的接口，返回空DataFrame
                    df = pd.DataFrame()
            
            logger.debug(f"获取基本信息成功")
            return df
            
        except Exception as e:
            logger.error(f"获取基本信息失败: {e}")
            return pd.DataFrame()
    
    def get_financial_data(self, code: str, report_type: str = 'income') -> pd.DataFrame:
        """
        获取财务数据
        
        Args:
            code: 股票代码
            report_type: 报表类型 ('income', 'balance', 'cashflow')
        
        Returns:
            pd.DataFrame: 财务数据
        """
        try:
            if self.ts_pro:
                ts_code = add_stock_suffix(code)
                if report_type == 'income':
                    df = self.ts_pro.income(ts_code=ts_code)
                elif report_type == 'balance':
                    df = self.ts_pro.balancesheet(ts_code=ts_code)
                elif report_type == 'cashflow':
                    df = self.ts_pro.cashflow(ts_code=ts_code)
                else:
                    df = pd.DataFrame()
            else:
                # AKShare财务数据接口
                if report_type == 'income':
                    df = ak.stock_financial_analysis_indicator(symbol=code)
                else:
                    df = pd.DataFrame()
            
            logger.debug(f"获取{code}财务数据({report_type})成功")
            return df
            
        except Exception as e:
            logger.error(f"获取{code}财务数据失败: {e}")
            return pd.DataFrame()
    
    def get_index_data(self, index_code: str = '000001', 
                      start_date: str = None, 
                      end_date: str = None) -> pd.DataFrame:
        """
        获取指数数据
        
        Args:
            index_code: 指数代码，默认为上证指数
            start_date: 开始日期
            end_date: 结束日期
        
        Returns:
            pd.DataFrame: 指数数据
        """
        try:
            if end_date is None:
                end_date = get_latest_trading_day()
            
            if start_date is None:
                start_date = (datetime.strptime(end_date, '%Y-%m-%d') - timedelta(days=365)).strftime('%Y-%m-%d')
            
            # 使用AKShare获取指数数据
            df = ak.stock_zh_index_daily(symbol=f"sh{index_code}")
            
            if not df.empty:
                df['date'] = pd.to_datetime(df['date'])
                df = df[(df['date'] >= start_date) & (df['date'] <= end_date)]
                df = df.sort_values('date')
            
            logger.debug(f"获取指数{index_code}数据成功")
            return df
            
        except Exception as e:
            logger.error(f"获取指数数据失败: {e}")
            return pd.DataFrame()


# 全局数据获取器实例
data_fetcher = DataFetcher()
