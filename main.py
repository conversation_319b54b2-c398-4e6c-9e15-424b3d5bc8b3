"""
A股选股策略系统主程序
"""
import uvicorn
from utils.config import config, ensure_directories
from utils.logger import get_logger
from api.main import app

logger = get_logger(__name__)


def main():
    """主程序入口"""
    try:
        # 确保必要目录存在
        ensure_directories()
        
        # 获取API配置
        api_config = config.get_api_config()
        host = api_config.get('host', '0.0.0.0')
        port = api_config.get('port', 8000)
        debug = api_config.get('debug', True)
        
        logger.info(f"启动A股选股策略API服务")
        logger.info(f"服务地址: http://{host}:{port}")
        logger.info(f"API文档: http://{host}:{port}/docs")
        
        # 启动API服务
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=debug,
            log_level="info"
        )
        
    except Exception as e:
        logger.error(f"启动服务失败: {e}")
        raise


if __name__ == "__main__":
    main()
