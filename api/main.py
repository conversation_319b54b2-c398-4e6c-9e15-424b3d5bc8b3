"""
FastAPI主应用
"""
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from datetime import datetime
import traceback
from typing import List, Dict, Any

from .models import (
    SelectionRequest, SelectionResponse, StrategyListResponse,
    HealthResponse, ErrorResponse, StockDataRequest, StockDataResponse,
    StockInfo, StrategyInfo
)
from strategies.base import strategy_manager
from strategies.technical import MomentumStrategy, BreakoutStrategy
from data.fetcher import data_fetcher
from utils.config import config
from utils.logger import get_logger
from utils.helpers import get_latest_trading_day

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="A股选股策略API",
    description="提供A股选股策略服务的API接口",
    version="1.0.0"
)

# 配置CORS
api_config = config.get_api_config()
cors_origins = api_config.get('cors_origins', ["*"])

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 注册策略
def register_strategies():
    """注册所有策略"""
    try:
        # 注册动量策略
        momentum_strategy = MomentumStrategy()
        strategy_manager.register_strategy(momentum_strategy)
        
        # 注册突破策略
        breakout_strategy = BreakoutStrategy()
        strategy_manager.register_strategy(breakout_strategy)
        
        logger.info("所有策略注册完成")
    except Exception as e:
        logger.error(f"策略注册失败: {e}")


# 应用启动时注册策略
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    register_strategies()
    logger.info("API服务启动完成")


# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"API异常: {exc}\n{traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            message="服务器内部错误",
            error_code="INTERNAL_ERROR",
            details={"error": str(exc)}
        ).dict()
    )


# 健康检查
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version="1.0.0"
    )


# 获取策略列表
@app.get("/strategies", response_model=StrategyListResponse)
async def get_strategies():
    """获取所有可用策略"""
    try:
        strategy_names = strategy_manager.list_strategies()
        strategies = []
        
        for name in strategy_names:
            strategy = strategy_manager.get_strategy(name)
            if strategy:
                info = strategy.get_strategy_info()
                strategies.append(StrategyInfo(**info))
        
        return StrategyListResponse(
            success=True,
            message="获取策略列表成功",
            data=strategies,
            total_count=len(strategies)
        )
        
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取策略列表失败")


# 执行选股
@app.post("/select", response_model=SelectionResponse)
async def select_stocks(request: SelectionRequest):
    """执行选股策略"""
    try:
        # 验证策略是否存在
        strategy = strategy_manager.get_strategy(request.strategy_name)
        if not strategy:
            raise HTTPException(status_code=404, detail=f"策略 {request.strategy_name} 不存在")
        
        # 设置选股日期
        date = request.date or get_latest_trading_day()
        
        # 更新策略参数
        if request.params:
            strategy.params.update(request.params)
        
        # 获取股票池
        stock_pool = data_fetcher.get_stock_list()
        if stock_pool.empty:
            raise HTTPException(status_code=500, detail="获取股票池失败")
        
        # 执行选股
        results = strategy_manager.run_strategy(request.strategy_name, stock_pool, date)
        
        # 转换为响应模型
        stock_infos = [StockInfo(**result) for result in results]
        
        return SelectionResponse(
            success=True,
            message="选股完成",
            data=stock_infos,
            strategy=request.strategy_name,
            date=date,
            total_count=len(stock_infos)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"选股失败: {e}")
        raise HTTPException(status_code=500, detail=f"选股失败: {str(e)}")


# 获取股票数据
@app.post("/stock-data", response_model=StockDataResponse)
async def get_stock_data(request: StockDataRequest):
    """获取股票历史数据"""
    try:
        # 获取股票数据
        data = data_fetcher.get_daily_data(
            request.code,
            request.start_date,
            request.end_date
        )
        
        if data.empty:
            raise HTTPException(status_code=404, detail=f"未找到股票 {request.code} 的数据")
        
        # 转换为字典列表
        data_list = data.to_dict('records')
        
        # 处理日期格式
        for record in data_list:
            if 'trade_date' in record:
                record['trade_date'] = record['trade_date'].strftime('%Y-%m-%d')
        
        return StockDataResponse(
            success=True,
            message="获取股票数据成功",
            data=data_list,
            code=request.code,
            total_count=len(data_list)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票数据失败: {str(e)}")


# 获取股票列表
@app.get("/stocks")
async def get_stock_list():
    """获取股票列表"""
    try:
        stock_list = data_fetcher.get_stock_list()
        
        if stock_list.empty:
            raise HTTPException(status_code=500, detail="获取股票列表失败")
        
        # 转换为字典列表
        stocks = stock_list.to_dict('records')
        
        return {
            "success": True,
            "message": "获取股票列表成功",
            "data": stocks,
            "total_count": len(stocks)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    
    host = api_config.get('host', '0.0.0.0')
    port = api_config.get('port', 8000)
    debug = api_config.get('debug', True)
    
    uvicorn.run(
        "api.main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
