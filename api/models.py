"""
API数据模型
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class StockInfo(BaseModel):
    """股票信息模型"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    score: float = Field(..., description="评分")
    reason: str = Field(..., description="选股理由")
    strategy: str = Field(..., description="策略名称")


class StrategyParams(BaseModel):
    """策略参数模型"""
    max_stocks: Optional[int] = Field(20, description="最大选股数量")
    exclude_st: Optional[bool] = Field(True, description="排除ST股票")
    exclude_new: Optional[bool] = Field(True, description="排除新股")
    min_market_cap: Optional[float] = Field(50, description="最小市值(亿元)")


class SelectionRequest(BaseModel):
    """选股请求模型"""
    strategy_name: str = Field(..., description="策略名称")
    date: Optional[str] = Field(None, description="选股日期，格式YYYY-MM-DD")
    params: Optional[Dict[str, Any]] = Field(None, description="策略参数")


class SelectionResponse(BaseModel):
    """选股响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: List[StockInfo] = Field(..., description="选股结果")
    strategy: str = Field(..., description="策略名称")
    date: str = Field(..., description="选股日期")
    total_count: int = Field(..., description="总数量")


class StrategyInfo(BaseModel):
    """策略信息模型"""
    name: str = Field(..., description="策略名称")
    description: str = Field(..., description="策略描述")
    type: str = Field(..., description="策略类型")
    params: Dict[str, Any] = Field(..., description="默认参数")


class StrategyListResponse(BaseModel):
    """策略列表响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: List[StrategyInfo] = Field(..., description="策略列表")
    total_count: int = Field(..., description="总数量")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(..., description="检查时间")
    version: str = Field(..., description="版本号")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class StockDataRequest(BaseModel):
    """股票数据请求模型"""
    code: str = Field(..., description="股票代码")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")


class StockDataResponse(BaseModel):
    """股票数据响应模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: List[Dict[str, Any]] = Field(..., description="股票数据")
    code: str = Field(..., description="股票代码")
    total_count: int = Field(..., description="数据条数")
