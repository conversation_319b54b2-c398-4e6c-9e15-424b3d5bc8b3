#!/bin/bash

# A股选股策略系统启动脚本

echo "=== A股选股策略系统 ==="
echo "正在启动服务..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查依赖
echo "检查依赖包..."
if ! python3 -c "import fastapi" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
fi

# 创建必要目录
mkdir -p data logs backtest_results exports

# 启动服务
echo "启动API服务..."
python3 main.py
