"""
策略基类模块
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import pandas as pd
from datetime import datetime
from utils.logger import get_logger
from utils.config import config

logger = get_logger(__name__)


class BaseStrategy(ABC):
    """选股策略基类"""
    
    def __init__(self, name: str, params: Dict[str, Any] = None):
        """
        初始化策略
        
        Args:
            name: 策略名称
            params: 策略参数
        """
        self.name = name
        self.params = params or {}
        self.strategy_config = config.get_strategy_config()
        self.default_params = self.strategy_config.get('default_params', {})
        
        # 合并默认参数和自定义参数
        self.params = {**self.default_params, **self.params}
        
        logger.info(f"初始化策略: {self.name}")
    
    @abstractmethod
    def select_stocks(self, stock_pool: pd.DataFrame, date: str = None) -> List[Dict[str, Any]]:
        """
        选股主方法
        
        Args:
            stock_pool: 股票池数据
            date: 选股日期，默认为最新交易日
        
        Returns:
            List[Dict]: 选中的股票列表，每个元素包含股票代码、名称、评分等信息
        """
        pass
    
    def filter_basic_conditions(self, stock_pool: pd.DataFrame) -> pd.DataFrame:
        """
        基础条件过滤
        
        Args:
            stock_pool: 股票池数据
        
        Returns:
            pd.DataFrame: 过滤后的股票池
        """
        filtered_pool = stock_pool.copy()
        
        # 排除ST股票
        if self.params.get('exclude_st', True):
            filtered_pool = filtered_pool[~filtered_pool['stock_name'].str.contains('ST|S', na=False)]
            logger.debug(f"排除ST股票后剩余: {len(filtered_pool)}只")
        
        # 排除新股
        if self.params.get('exclude_new', True):
            if 'list_date' in filtered_pool.columns:
                current_date = datetime.now()
                filtered_pool['list_date'] = pd.to_datetime(filtered_pool['list_date'])
                days_since_listing = (current_date - filtered_pool['list_date']).dt.days
                filtered_pool = filtered_pool[days_since_listing >= 60]
                logger.debug(f"排除新股后剩余: {len(filtered_pool)}只")
        
        # 最小市值过滤
        min_market_cap = self.params.get('min_market_cap', 0)
        if min_market_cap > 0 and 'market_cap' in filtered_pool.columns:
            filtered_pool = filtered_pool[filtered_pool['market_cap'] >= min_market_cap * 1e8]
            logger.debug(f"市值过滤后剩余: {len(filtered_pool)}只")
        
        return filtered_pool
    
    def rank_stocks(self, stocks: pd.DataFrame, score_column: str = 'score') -> pd.DataFrame:
        """
        股票排序
        
        Args:
            stocks: 股票数据
            score_column: 评分列名
        
        Returns:
            pd.DataFrame: 排序后的股票数据
        """
        if score_column in stocks.columns:
            return stocks.sort_values(score_column, ascending=False)
        else:
            logger.warning(f"评分列 {score_column} 不存在，返回原始数据")
            return stocks
    
    def limit_stock_count(self, stocks: pd.DataFrame) -> pd.DataFrame:
        """
        限制选股数量
        
        Args:
            stocks: 股票数据
        
        Returns:
            pd.DataFrame: 限制数量后的股票数据
        """
        max_stocks = self.params.get('max_stocks', 20)
        return stocks.head(max_stocks)
    
    def format_result(self, stocks: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        格式化选股结果
        
        Args:
            stocks: 股票数据
        
        Returns:
            List[Dict]: 格式化后的结果
        """
        results = []
        for _, stock in stocks.iterrows():
            result = {
                'code': stock.get('code', ''),
                'name': stock.get('stock_name', ''),
                'score': stock.get('score', 0),
                'reason': stock.get('reason', ''),
                'strategy': self.name
            }
            results.append(result)
        
        return results
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """
        获取策略信息
        
        Returns:
            Dict: 策略信息
        """
        return {
            'name': self.name,
            'params': self.params,
            'description': self.__doc__ or '',
            'type': self.__class__.__name__
        }


class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies = {}
        logger.info("策略管理器初始化完成")
    
    def register_strategy(self, strategy: BaseStrategy):
        """
        注册策略
        
        Args:
            strategy: 策略实例
        """
        self.strategies[strategy.name] = strategy
        logger.info(f"注册策略: {strategy.name}")
    
    def get_strategy(self, name: str) -> Optional[BaseStrategy]:
        """
        获取策略
        
        Args:
            name: 策略名称
        
        Returns:
            BaseStrategy: 策略实例
        """
        return self.strategies.get(name)
    
    def list_strategies(self) -> List[str]:
        """
        列出所有策略
        
        Returns:
            List[str]: 策略名称列表
        """
        return list(self.strategies.keys())
    
    def run_strategy(self, name: str, stock_pool: pd.DataFrame, date: str = None) -> List[Dict[str, Any]]:
        """
        运行策略
        
        Args:
            name: 策略名称
            stock_pool: 股票池
            date: 选股日期
        
        Returns:
            List[Dict]: 选股结果
        """
        strategy = self.get_strategy(name)
        if not strategy:
            logger.error(f"策略 {name} 不存在")
            return []
        
        try:
            results = strategy.select_stocks(stock_pool, date)
            logger.info(f"策略 {name} 运行完成，选出 {len(results)} 只股票")
            return results
        except Exception as e:
            logger.error(f"策略 {name} 运行失败: {e}")
            return []


# 全局策略管理器实例
strategy_manager = StrategyManager()
