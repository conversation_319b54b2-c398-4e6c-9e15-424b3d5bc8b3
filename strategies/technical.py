"""
技术面选股策略
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any
from .base import BaseStrategy
from data.fetcher import data_fetcher
from utils.logger import get_logger
from utils.helpers import get_latest_trading_day

logger = get_logger(__name__)


class MomentumStrategy(BaseStrategy):
    """动量选股策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        default_params = {
            'lookback_days': 20,  # 回看天数
            'min_return': 0.05,   # 最小收益率
            'max_return': 0.30,   # 最大收益率
            'volume_factor': 1.5  # 成交量放大倍数
        }
        
        if params:
            default_params.update(params)
        
        super().__init__("动量选股策略", default_params)
    
    def select_stocks(self, stock_pool: pd.DataFrame, date: str = None) -> List[Dict[str, Any]]:
        """
        动量选股主逻辑
        
        选股条件：
        1. 近期涨幅在合理区间内
        2. 成交量有所放大
        3. 价格突破短期均线
        """
        if date is None:
            date = get_latest_trading_day()
        
        logger.info(f"开始执行动量选股策略，日期: {date}")
        
        # 基础条件过滤
        filtered_pool = self.filter_basic_conditions(stock_pool)
        
        selected_stocks = []
        
        for _, stock in filtered_pool.iterrows():
            try:
                code = stock['code']
                
                # 获取历史数据
                hist_data = data_fetcher.get_daily_data(
                    code, 
                    start_date=(pd.to_datetime(date) - pd.Timedelta(days=60)).strftime('%Y-%m-%d'),
                    end_date=date
                )
                
                if hist_data.empty or len(hist_data) < 30:
                    continue
                
                # 计算技术指标
                score = self._calculate_momentum_score(hist_data)
                
                if score > 0:
                    selected_stocks.append({
                        'code': code,
                        'stock_name': stock.get('stock_name', ''),
                        'score': score,
                        'reason': self._get_selection_reason(hist_data)
                    })
                    
            except Exception as e:
                logger.debug(f"处理股票 {stock.get('code', '')} 时出错: {e}")
                continue
        
        # 转换为DataFrame进行排序和限制
        if selected_stocks:
            df_selected = pd.DataFrame(selected_stocks)
            df_selected = self.rank_stocks(df_selected)
            df_selected = self.limit_stock_count(df_selected)
            return self.format_result(df_selected)
        
        return []
    
    def _calculate_momentum_score(self, data: pd.DataFrame) -> float:
        """
        计算动量评分
        
        Args:
            data: 股票历史数据
        
        Returns:
            float: 动量评分
        """
        if len(data) < 20:
            return 0
        
        # 确保数据按日期排序
        data = data.sort_values('trade_date')
        
        # 计算收益率
        lookback_days = self.params['lookback_days']
        recent_return = (data['close'].iloc[-1] / data['close'].iloc[-lookback_days] - 1)
        
        # 收益率过滤
        min_return = self.params['min_return']
        max_return = self.params['max_return']
        
        if recent_return < min_return or recent_return > max_return:
            return 0
        
        score = 0
        
        # 1. 收益率评分 (30%)
        return_score = min(recent_return / max_return, 1.0) * 30
        score += return_score
        
        # 2. 成交量评分 (25%)
        volume_score = self._calculate_volume_score(data) * 25
        score += volume_score
        
        # 3. 均线评分 (25%)
        ma_score = self._calculate_ma_score(data) * 25
        score += ma_score
        
        # 4. 相对强度评分 (20%)
        rs_score = self._calculate_relative_strength_score(data) * 20
        score += rs_score
        
        return score
    
    def _calculate_volume_score(self, data: pd.DataFrame) -> float:
        """计算成交量评分"""
        try:
            # 计算近期平均成交量与历史平均成交量的比值
            recent_volume = data['vol'].tail(5).mean()
            hist_volume = data['vol'].head(-5).mean()
            
            volume_ratio = recent_volume / hist_volume if hist_volume > 0 else 0
            volume_factor = self.params['volume_factor']
            
            if volume_ratio >= volume_factor:
                return 1.0
            elif volume_ratio >= 1.0:
                return (volume_ratio - 1.0) / (volume_factor - 1.0)
            else:
                return 0
                
        except Exception:
            return 0
    
    def _calculate_ma_score(self, data: pd.DataFrame) -> float:
        """计算均线评分"""
        try:
            # 计算5日和20日均线
            data['ma5'] = data['close'].rolling(5).mean()
            data['ma20'] = data['close'].rolling(20).mean()
            
            current_price = data['close'].iloc[-1]
            ma5 = data['ma5'].iloc[-1]
            ma20 = data['ma20'].iloc[-1]
            
            score = 0
            
            # 价格在均线之上
            if current_price > ma5:
                score += 0.5
            if current_price > ma20:
                score += 0.3
            
            # 短期均线在长期均线之上
            if ma5 > ma20:
                score += 0.2
            
            return score
            
        except Exception:
            return 0
    
    def _calculate_relative_strength_score(self, data: pd.DataFrame) -> float:
        """计算相对强度评分"""
        try:
            # 简化的相对强度计算
            # 比较近期表现与历史表现
            recent_performance = data['close'].tail(10).pct_change().mean()
            hist_performance = data['close'].head(-10).pct_change().mean()
            
            if hist_performance != 0:
                rs_ratio = recent_performance / abs(hist_performance)
                return min(max(rs_ratio, 0), 1.0)
            
            return 0.5
            
        except Exception:
            return 0
    
    def _get_selection_reason(self, data: pd.DataFrame) -> str:
        """获取选股理由"""
        try:
            lookback_days = self.params['lookback_days']
            recent_return = (data['close'].iloc[-1] / data['close'].iloc[-lookback_days] - 1)
            
            recent_volume = data['vol'].tail(5).mean()
            hist_volume = data['vol'].head(-5).mean()
            volume_ratio = recent_volume / hist_volume if hist_volume > 0 else 1
            
            return f"近{lookback_days}日涨幅{recent_return:.2%}，成交量放大{volume_ratio:.1f}倍"
            
        except Exception:
            return "技术面表现良好"


class BreakoutStrategy(BaseStrategy):
    """突破选股策略"""
    
    def __init__(self, params: Dict[str, Any] = None):
        default_params = {
            'breakout_period': 20,  # 突破周期
            'volume_threshold': 2.0,  # 成交量阈值
            'price_threshold': 0.03   # 价格突破阈值
        }
        
        if params:
            default_params.update(params)
        
        super().__init__("突破选股策略", default_params)
    
    def select_stocks(self, stock_pool: pd.DataFrame, date: str = None) -> List[Dict[str, Any]]:
        """
        突破选股主逻辑
        
        选股条件：
        1. 价格突破近期高点
        2. 成交量显著放大
        3. 突破幅度适中
        """
        if date is None:
            date = get_latest_trading_day()
        
        logger.info(f"开始执行突破选股策略，日期: {date}")
        
        # 基础条件过滤
        filtered_pool = self.filter_basic_conditions(stock_pool)
        
        selected_stocks = []
        
        for _, stock in filtered_pool.iterrows():
            try:
                code = stock['code']
                
                # 获取历史数据
                hist_data = data_fetcher.get_daily_data(
                    code,
                    start_date=(pd.to_datetime(date) - pd.Timedelta(days=60)).strftime('%Y-%m-%d'),
                    end_date=date
                )
                
                if hist_data.empty or len(hist_data) < 30:
                    continue
                
                # 检查是否满足突破条件
                if self._check_breakout_conditions(hist_data):
                    score = self._calculate_breakout_score(hist_data)
                    
                    selected_stocks.append({
                        'code': code,
                        'stock_name': stock.get('stock_name', ''),
                        'score': score,
                        'reason': self._get_breakout_reason(hist_data)
                    })
                    
            except Exception as e:
                logger.debug(f"处理股票 {stock.get('code', '')} 时出错: {e}")
                continue
        
        # 转换为DataFrame进行排序和限制
        if selected_stocks:
            df_selected = pd.DataFrame(selected_stocks)
            df_selected = self.rank_stocks(df_selected)
            df_selected = self.limit_stock_count(df_selected)
            return self.format_result(df_selected)
        
        return []
    
    def _check_breakout_conditions(self, data: pd.DataFrame) -> bool:
        """检查突破条件"""
        try:
            data = data.sort_values('trade_date')
            
            breakout_period = self.params['breakout_period']
            current_price = data['close'].iloc[-1]
            
            # 计算前期高点
            period_high = data['high'].iloc[-breakout_period:-1].max()
            
            # 检查价格突破
            price_threshold = self.params['price_threshold']
            price_breakout = (current_price / period_high - 1) >= price_threshold
            
            # 检查成交量放大
            volume_threshold = self.params['volume_threshold']
            recent_volume = data['vol'].iloc[-1]
            avg_volume = data['vol'].iloc[-breakout_period:-1].mean()
            volume_breakout = recent_volume >= (avg_volume * volume_threshold)
            
            return price_breakout and volume_breakout
            
        except Exception:
            return False
    
    def _calculate_breakout_score(self, data: pd.DataFrame) -> float:
        """计算突破评分"""
        try:
            data = data.sort_values('trade_date')
            
            breakout_period = self.params['breakout_period']
            current_price = data['close'].iloc[-1]
            period_high = data['high'].iloc[-breakout_period:-1].max()
            
            # 突破幅度评分
            breakout_ratio = (current_price / period_high - 1)
            breakout_score = min(breakout_ratio * 100, 50)  # 最高50分
            
            # 成交量评分
            recent_volume = data['vol'].iloc[-1]
            avg_volume = data['vol'].iloc[-breakout_period:-1].mean()
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            volume_score = min((volume_ratio - 1) * 10, 30)  # 最高30分
            
            # 持续性评分
            consistency_score = self._calculate_consistency_score(data) * 20  # 最高20分
            
            return breakout_score + volume_score + consistency_score
            
        except Exception:
            return 0
    
    def _calculate_consistency_score(self, data: pd.DataFrame) -> float:
        """计算突破一致性评分"""
        try:
            # 检查近几日是否持续上涨
            recent_returns = data['close'].tail(3).pct_change().dropna()
            positive_days = (recent_returns > 0).sum()
            
            return positive_days / len(recent_returns) if len(recent_returns) > 0 else 0
            
        except Exception:
            return 0
    
    def _get_breakout_reason(self, data: pd.DataFrame) -> str:
        """获取突破选股理由"""
        try:
            breakout_period = self.params['breakout_period']
            current_price = data['close'].iloc[-1]
            period_high = data['high'].iloc[-breakout_period:-1].max()
            breakout_ratio = (current_price / period_high - 1)
            
            recent_volume = data['vol'].iloc[-1]
            avg_volume = data['vol'].iloc[-breakout_period:-1].mean()
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            
            return f"突破{breakout_period}日高点{breakout_ratio:.2%}，成交量放大{volume_ratio:.1f}倍"
            
        except Exception:
            return "价格突破，成交量放大"
