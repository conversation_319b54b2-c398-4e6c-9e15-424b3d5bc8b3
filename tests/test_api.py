"""
API接口测试
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)


def test_health_check():
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_get_strategies():
    """测试获取策略列表接口"""
    response = client.get("/strategies")
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "data" in data
    assert isinstance(data["data"], list)


def test_get_stock_list():
    """测试获取股票列表接口"""
    response = client.get("/stocks")
    # 注意：这个测试可能因为网络或数据源问题而失败
    # 在实际环境中需要mock数据源
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data


def test_select_stocks():
    """测试选股接口"""
    request_data = {
        "strategy_name": "动量选股策略",
        "params": {
            "max_stocks": 5
        }
    }
    
    response = client.post("/select", json=request_data)
    # 注意：这个测试可能因为数据源问题而失败
    # 在实际环境中需要mock数据源
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert data["strategy"] == "动量选股策略"


def test_select_stocks_invalid_strategy():
    """测试无效策略名称"""
    request_data = {
        "strategy_name": "不存在的策略"
    }
    
    response = client.post("/select", json=request_data)
    assert response.status_code == 404
