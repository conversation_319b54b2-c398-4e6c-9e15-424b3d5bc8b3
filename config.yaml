# A股选股策略系统配置文件

# 数据源配置
data_sources:
  tushare:
    token: "your_tushare_token_here"  # 需要在tushare官网申请
    enabled: true
  
  akshare:
    enabled: true

# 数据库配置
database:
  type: "sqlite"  # sqlite, postgresql
  sqlite:
    path: "data/stocks.db"
  postgresql:
    host: "localhost"
    port: 5432
    database: "stocks"
    username: "postgres"
    password: "password"

# Redis配置
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null

# API配置
api:
  host: "0.0.0.0"
  port: 8000
  debug: true
  cors_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"

# 策略配置
strategies:
  # 默认策略参数
  default_params:
    max_stocks: 20  # 最大选股数量
    min_market_cap: 50  # 最小市值(亿元)
    exclude_st: true  # 排除ST股票
    exclude_new: true  # 排除新股(上市不足60天)
  
  # 技术指标参数
  technical:
    ma_periods: [5, 10, 20, 60]  # 移动平均线周期
    rsi_period: 14  # RSI周期
    macd_params: [12, 26, 9]  # MACD参数
  
  # 基本面参数
  fundamental:
    min_roe: 0.1  # 最小ROE
    max_pe: 30  # 最大PE
    min_revenue_growth: 0.1  # 最小营收增长率

# 任务调度配置
scheduler:
  timezone: "Asia/Shanghai"
  jobs:
    daily_update:
      hour: 18  # 每天18点更新数据
      minute: 0
    strategy_run:
      hour: 19  # 每天19点运行策略
      minute: 0

# 日志配置
logging:
  level: "INFO"
  file: "logs/app.log"
  max_size: "10MB"
  backup_count: 5

# 风险控制
risk_control:
  max_position_ratio: 0.1  # 单只股票最大仓位比例
  max_sector_ratio: 0.3  # 单个行业最大仓位比例
  stop_loss_ratio: -0.1  # 止损比例
