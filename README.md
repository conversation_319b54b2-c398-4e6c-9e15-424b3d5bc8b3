# A股选股策略系统

## 项目概述
这是一个针对A股市场的每日选股策略系统，提供策略开发、回测、API服务等功能。

## 系统架构

### 核心模块
1. **数据模块** (`data/`)
   - 股票数据获取
   - 技术指标计算
   - 数据存储管理

2. **策略模块** (`strategies/`)
   - 选股策略实现
   - 策略评估框架
   - 回测引擎

3. **API模块** (`api/`)
   - RESTful API服务
   - 认证和权限
   - 数据接口

4. **工具模块** (`utils/`)
   - 通用工具函数
   - 配置管理
   - 日志系统

## 技术栈
- **后端**: Python + FastAPI
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **数据源**: tushare / akshare
- **缓存**: Redis
- **任务调度**: APScheduler
- **前端**: React (可选)

## 快速开始

### 环境要求
- Python 3.8+
- pip

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行服务
```bash
python main.py
```

## 开发计划

### Phase 1: 基础框架 (当前阶段)
- [x] 项目结构搭建
- [ ] 数据获取模块
- [ ] 基础API框架
- [ ] 简单选股策略

### Phase 2: 策略扩展
- [ ] 多种选股策略
- [ ] 策略回测框架
- [ ] 策略评估指标

### Phase 3: 高级功能
- [ ] 策略组合
- [ ] 风险控制
- [ ] 实时监控
- [ ] Web界面

## 目录结构
```
pickup/
├── data/                   # 数据模块
│   ├── __init__.py
│   ├── fetcher.py         # 数据获取
│   ├── processor.py       # 数据处理
│   └── storage.py         # 数据存储
├── strategies/            # 策略模块
│   ├── __init__.py
│   ├── base.py           # 策略基类
│   ├── technical.py      # 技术面策略
│   └── fundamental.py    # 基本面策略
├── api/                  # API模块
│   ├── __init__.py
│   ├── main.py          # API主程序
│   ├── routes/          # 路由定义
│   └── models/          # 数据模型
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── config.py        # 配置管理
│   ├── logger.py        # 日志系统
│   └── helpers.py       # 辅助函数
├── tests/               # 测试
├── docs/                # 文档
├── requirements.txt     # 依赖包
├── config.yaml         # 配置文件
└── main.py             # 主程序入口
```
