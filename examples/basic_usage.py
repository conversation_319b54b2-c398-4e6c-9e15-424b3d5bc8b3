"""
基础使用示例
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategies.technical import MomentumStrategy, BreakoutStrategy
from strategies.base import strategy_manager
from data.fetcher import data_fetcher
from utils.logger import get_logger

logger = get_logger(__name__)


def example_momentum_strategy():
    """动量策略示例"""
    print("=== 动量选股策略示例 ===")
    
    # 创建策略实例
    strategy = MomentumStrategy({
        'max_stocks': 5,
        'lookback_days': 15,
        'min_return': 0.03
    })
    
    # 注册策略
    strategy_manager.register_strategy(strategy)
    
    try:
        # 获取股票池
        print("正在获取股票池...")
        stock_pool = data_fetcher.get_stock_list()
        
        if stock_pool.empty:
            print("获取股票池失败，请检查数据源配置")
            return
        
        print(f"股票池大小: {len(stock_pool)}")
        
        # 执行选股
        print("正在执行选股...")
        results = strategy.select_stocks(stock_pool)
        
        # 显示结果
        print(f"\n选股结果 (共{len(results)}只):")
        print("-" * 80)
        print(f"{'代码':<10} {'名称':<15} {'评分':<10} {'选股理由'}")
        print("-" * 80)
        
        for stock in results:
            print(f"{stock['code']:<10} {stock['name']:<15} {stock['score']:<10.2f} {stock['reason']}")
        
    except Exception as e:
        print(f"执行失败: {e}")
        logger.error(f"动量策略示例执行失败: {e}")


def example_breakout_strategy():
    """突破策略示例"""
    print("\n=== 突破选股策略示例 ===")
    
    # 创建策略实例
    strategy = BreakoutStrategy({
        'max_stocks': 3,
        'breakout_period': 15,
        'volume_threshold': 1.8
    })
    
    # 注册策略
    strategy_manager.register_strategy(strategy)
    
    try:
        # 获取股票池
        print("正在获取股票池...")
        stock_pool = data_fetcher.get_stock_list()
        
        if stock_pool.empty:
            print("获取股票池失败，请检查数据源配置")
            return
        
        # 为了演示，只使用前100只股票
        stock_pool = stock_pool.head(100)
        print(f"股票池大小: {len(stock_pool)} (演示用)")
        
        # 执行选股
        print("正在执行选股...")
        results = strategy.select_stocks(stock_pool)
        
        # 显示结果
        print(f"\n选股结果 (共{len(results)}只):")
        print("-" * 80)
        print(f"{'代码':<10} {'名称':<15} {'评分':<10} {'选股理由'}")
        print("-" * 80)
        
        for stock in results:
            print(f"{stock['code']:<10} {stock['name']:<15} {stock['score']:<10.2f} {stock['reason']}")
        
    except Exception as e:
        print(f"执行失败: {e}")
        logger.error(f"突破策略示例执行失败: {e}")


def example_strategy_manager():
    """策略管理器示例"""
    print("\n=== 策略管理器示例 ===")
    
    # 列出所有策略
    strategies = strategy_manager.list_strategies()
    print(f"已注册策略: {strategies}")
    
    # 获取策略信息
    for name in strategies:
        strategy = strategy_manager.get_strategy(name)
        if strategy:
            info = strategy.get_strategy_info()
            print(f"\n策略: {info['name']}")
            print(f"类型: {info['type']}")
            print(f"参数: {info['params']}")


def main():
    """主函数"""
    print("A股选股策略系统 - 基础使用示例")
    print("=" * 50)
    
    try:
        # 动量策略示例
        example_momentum_strategy()
        
        # 突破策略示例
        example_breakout_strategy()
        
        # 策略管理器示例
        example_strategy_manager()
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"示例执行失败: {e}")
        logger.error(f"示例执行失败: {e}")


if __name__ == "__main__":
    main()
