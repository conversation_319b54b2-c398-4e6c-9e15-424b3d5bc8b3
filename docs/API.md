# API接口文档

## 概述
A股选股策略系统提供RESTful API接口，支持策略查询、选股执行、数据获取等功能。

## 基础信息
- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`

## 接口列表

### 1. 健康检查
检查服务状态

**请求**
```
GET /health
```

**响应**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "version": "1.0.0"
}
```

### 2. 获取策略列表
获取所有可用的选股策略

**请求**
```
GET /strategies
```

**响应**
```json
{
  "success": true,
  "message": "获取策略列表成功",
  "data": [
    {
      "name": "动量选股策略",
      "description": "基于价格动量的选股策略",
      "type": "MomentumStrategy",
      "params": {
        "lookback_days": 20,
        "min_return": 0.05,
        "max_return": 0.30
      }
    }
  ],
  "total_count": 1
}
```

### 3. 执行选股
根据指定策略执行选股

**请求**
```
POST /select
Content-Type: application/json

{
  "strategy_name": "动量选股策略",
  "date": "2024-01-01",
  "params": {
    "max_stocks": 10,
    "min_return": 0.03
  }
}
```

**参数说明**
- `strategy_name`: 策略名称（必填）
- `date`: 选股日期，格式YYYY-MM-DD（可选，默认为最新交易日）
- `params`: 策略参数（可选，会覆盖默认参数）

**响应**
```json
{
  "success": true,
  "message": "选股完成",
  "data": [
    {
      "code": "000001",
      "name": "平安银行",
      "score": 85.5,
      "reason": "近20日涨幅8.5%，成交量放大2.1倍",
      "strategy": "动量选股策略"
    }
  ],
  "strategy": "动量选股策略",
  "date": "2024-01-01",
  "total_count": 1
}
```

### 4. 获取股票列表
获取所有A股股票列表

**请求**
```
GET /stocks
```

**响应**
```json
{
  "success": true,
  "message": "获取股票列表成功",
  "data": [
    {
      "code": "000001",
      "stock_name": "平安银行",
      "ts_code": "000001.SZ"
    }
  ],
  "total_count": 1
}
```

### 5. 获取股票数据
获取指定股票的历史数据

**请求**
```
POST /stock-data
Content-Type: application/json

{
  "code": "000001",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

**参数说明**
- `code`: 股票代码（必填）
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）

**响应**
```json
{
  "success": true,
  "message": "获取股票数据成功",
  "data": [
    {
      "trade_date": "2024-01-01",
      "open": 10.0,
      "close": 10.5,
      "high": 10.8,
      "low": 9.9,
      "vol": 1000000
    }
  ],
  "code": "000001",
  "total_count": 1
}
```

## 错误响应
当请求失败时，API会返回错误响应：

```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    "error": "详细错误信息"
  }
}
```

## 状态码
- `200`: 请求成功
- `404`: 资源不存在
- `422`: 请求参数错误
- `500`: 服务器内部错误

## 使用示例

### Python示例
```python
import requests

# 获取策略列表
response = requests.get("http://localhost:8000/strategies")
strategies = response.json()

# 执行选股
data = {
    "strategy_name": "动量选股策略",
    "params": {"max_stocks": 5}
}
response = requests.post("http://localhost:8000/select", json=data)
result = response.json()
```

### curl示例
```bash
# 健康检查
curl http://localhost:8000/health

# 获取策略列表
curl http://localhost:8000/strategies

# 执行选股
curl -X POST http://localhost:8000/select \
  -H "Content-Type: application/json" \
  -d '{"strategy_name": "动量选股策略", "params": {"max_stocks": 5}}'
```
