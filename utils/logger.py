"""
日志系统模块
"""
import sys
from pathlib import Path
from loguru import logger
from .config import config, get_logs_dir


def setup_logger():
    """设置日志系统"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.get_logging_config()
    log_level = log_config.get('level', 'INFO')
    log_file = log_config.get('file', 'logs/app.log')
    max_size = log_config.get('max_size', '10MB')
    backup_count = log_config.get('backup_count', 5)
    
    # 确保日志目录存在
    log_path = Path(log_file)
    log_path.parent.mkdir(exist_ok=True)
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件输出
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=max_size,
        retention=backup_count,
        compression="zip",
        encoding="utf-8"
    )
    
    return logger


# 初始化日志系统
setup_logger()


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger
