"""
配置管理模块
"""
import yaml
import os
from typing import Dict, Any
from pathlib import Path


class Config:
    """配置管理类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def get_data_source_config(self) -> Dict[str, Any]:
        """获取数据源配置"""
        return self.get('data_sources', {})
    
    def get_strategy_config(self) -> Dict[str, Any]:
        """获取策略配置"""
        return self.get('strategies', {})
    
    def get_redis_config(self) -> Dict[str, Any]:
        """获取Redis配置"""
        return self.get('redis', {})
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度器配置"""
        return self.get('scheduler', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_risk_control_config(self) -> Dict[str, Any]:
        """获取风险控制配置"""
        return self.get('risk_control', {})


# 全局配置实例
config = Config()


def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        'data',
        'logs',
        'backtest_results',
        'exports'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)


def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent


def get_data_dir() -> Path:
    """获取数据目录"""
    return get_project_root() / 'data'


def get_logs_dir() -> Path:
    """获取日志目录"""
    return get_project_root() / 'logs'
