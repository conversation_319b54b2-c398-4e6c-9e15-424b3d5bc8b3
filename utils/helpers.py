"""
辅助函数模块
"""
import re
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Union
from .logger import get_logger

logger = get_logger(__name__)


def is_trading_day(date: Union[str, datetime] = None) -> bool:
    """
    判断是否为交易日
    
    Args:
        date: 日期，默认为今天
    
    Returns:
        bool: 是否为交易日
    """
    if date is None:
        date = datetime.now()
    elif isinstance(date, str):
        date = datetime.strptime(date, '%Y-%m-%d')
    
    # 简单判断：周一到周五为交易日（不考虑节假日）
    # 实际应用中应该使用更准确的交易日历
    return date.weekday() < 5


def get_latest_trading_day(date: Union[str, datetime] = None) -> str:
    """
    获取最近的交易日
    
    Args:
        date: 基准日期，默认为今天
    
    Returns:
        str: 最近交易日，格式为 YYYY-MM-DD
    """
    if date is None:
        date = datetime.now()
    elif isinstance(date, str):
        date = datetime.strptime(date, '%Y-%m-%d')
    
    while not is_trading_day(date):
        date -= timedelta(days=1)
    
    return date.strftime('%Y-%m-%d')


def normalize_stock_code(code: str) -> str:
    """
    标准化股票代码
    
    Args:
        code: 股票代码
    
    Returns:
        str: 标准化后的股票代码
    """
    # 移除所有非数字字符
    code = re.sub(r'[^\d]', '', code)
    
    # 确保是6位数字
    if len(code) == 6:
        return code
    elif len(code) < 6:
        return code.zfill(6)
    else:
        return code[:6]


def add_stock_suffix(code: str) -> str:
    """
    为股票代码添加后缀
    
    Args:
        code: 6位股票代码
    
    Returns:
        str: 带后缀的股票代码
    """
    code = normalize_stock_code(code)
    
    if code.startswith('6'):
        return f"{code}.SH"  # 上海交易所
    elif code.startswith(('0', '3')):
        return f"{code}.SZ"  # 深圳交易所
    else:
        return code


def remove_stock_suffix(code: str) -> str:
    """
    移除股票代码后缀
    
    Args:
        code: 带后缀的股票代码
    
    Returns:
        str: 6位股票代码
    """
    return code.split('.')[0]


def is_st_stock(name: str) -> bool:
    """
    判断是否为ST股票
    
    Args:
        name: 股票名称
    
    Returns:
        bool: 是否为ST股票
    """
    if not name:
        return False
    
    st_patterns = ['ST', '*ST', 'S*ST', 'SST', 'S']
    return any(pattern in name.upper() for pattern in st_patterns)


def calculate_returns(prices: pd.Series, periods: int = 1) -> pd.Series:
    """
    计算收益率
    
    Args:
        prices: 价格序列
        periods: 计算周期
    
    Returns:
        pd.Series: 收益率序列
    """
    return prices.pct_change(periods=periods)


def calculate_volatility(returns: pd.Series, window: int = 20) -> pd.Series:
    """
    计算波动率
    
    Args:
        returns: 收益率序列
        window: 计算窗口
    
    Returns:
        pd.Series: 波动率序列
    """
    return returns.rolling(window=window).std()


def format_number(num: float, precision: int = 2) -> str:
    """
    格式化数字显示
    
    Args:
        num: 数字
        precision: 小数位数
    
    Returns:
        str: 格式化后的字符串
    """
    if abs(num) >= 1e8:
        return f"{num/1e8:.{precision}f}亿"
    elif abs(num) >= 1e4:
        return f"{num/1e4:.{precision}f}万"
    else:
        return f"{num:.{precision}f}"


def safe_divide(a: float, b: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        a: 被除数
        b: 除数
        default: 默认值
    
    Returns:
        float: 除法结果
    """
    try:
        if b == 0:
            return default
        return a / b
    except (TypeError, ZeroDivisionError):
        return default


def validate_date_range(start_date: str, end_date: str) -> bool:
    """
    验证日期范围
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    
    Returns:
        bool: 日期范围是否有效
    """
    try:
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')
        return start <= end
    except ValueError:
        return False
